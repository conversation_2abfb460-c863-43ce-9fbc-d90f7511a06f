package com.jd.qf.ai.server.common.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 质检类型
 * <AUTHOR>
 * @date 2024-12-25 16:23
 */
@Getter
@AllArgsConstructor
public enum InspectContentTypeEnum {
    /**
     * 文本
     */
    TEXT("text", "文本质检"),
    /**
     * 图片
     */
    IMAGE("image", "图片质检"),

    /**
     * 动图
     */
    GIF("gif", "动图质检")
    ;

    /**
     * CODE
     */
    private final String code;
    /**
     * MSG
     */
    private final String msg;

    public static String getMsgByCode(String code) {
        for (InspectContentTypeEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e.getMsg();
            }
        }
        return null;
    }
}
