package com.jd.qf.ai.server.common.pojo.resp;

import com.jd.qf.ai.server.common.pojo.constants.RespCodeConstants;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>业务返回类</p>
 * <p>success() 成功</p>
 * <p>error() 系统异常</p>
 *
 * <AUTHOR>
 * @date 2021-1-29
 */
@Data
@NoArgsConstructor
public class BizResponse<T> {

    /**
     * 返回码
     */
    private Integer code;

    /**
     * 返回信息
     */
    private String msg;

    /**
     * 返回数据
     */
    private T data;

    public BizResponse(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public static<T> BizResponse<T> success() {
        return new BizResponse<>(RespCodeConstants.SUCCESS, "成功", null);
    }

    public static <T> BizResponse<T> success(T data) {
        return new BizResponse<>(RespCodeConstants.SUCCESS, "成功", data);
    }

    public static<T> BizResponse<T> error(String msg) {
        return new BizResponse<>(RespCodeConstants.ERROR, msg, null);
    }

    public static<T> BizResponse<T> error(Integer code, String msg) {
        return new BizResponse<>(code, msg, null);
    }

}
