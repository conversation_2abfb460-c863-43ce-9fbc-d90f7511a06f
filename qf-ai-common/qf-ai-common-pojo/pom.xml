<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.jd.wdy</groupId>
    <artifactId>qf-ai-common-pojo</artifactId>
    <version>1.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <fastjson.version>1.2.83-jdsec.rc1</fastjson.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-crypto</artifactId>
            <version>5.8.24</version>
        </dependency>
        <dependency>
            <artifactId>fastjson</artifactId>
            <groupId>com.alibaba</groupId>
            <version>${fastjson.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.jdt.open</groupId>
            <artifactId>open-dto</artifactId>
            <version>2.3.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.jdt.open</groupId>
            <artifactId>open-exception</artifactId>
            <version>2.3.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>2.0.1.Final</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>Release Repository</name>
            <url>https://artifactory.jd.com/libs-releases</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>JD maven2 repository-snapshots</name>
            <url>https://artifactory.jd.com/libs-snapshots</url>
        </snapshotRepository>
    </distributionManagement>
</project>