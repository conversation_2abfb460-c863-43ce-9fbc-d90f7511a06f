package com.jd.qf.ai.biz.facade.controller.wash;

import cn.hutool.core.util.PageUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.jd.qf.ai.biz.common.enums.AiChatDataStatusEnum;
import com.jd.qf.ai.biz.infrastructure.dao.mapper.AiChatRecordMapper;
import com.jd.qf.ai.biz.infrastructure.dao.mapper.CsChatRecordNewMapper;
import com.jd.qf.ai.biz.infrastructure.dao.po.AiChatRecordPo;
import com.jd.qf.ai.biz.infrastructure.dao.po.AiChatRecordQueryPo;
import com.jd.qf.ai.biz.infrastructure.dao.po.AiKnowQaPo;
import com.jd.qf.ai.biz.infrastructure.dao.po.CsChatRecordPo;
import com.jd.qf.ai.server.common.lang.pageutil.DBPageUtils;
import com.jd.qf.ai.server.common.lang.util.CollectionUtil;
import com.jd.qf.ai.server.common.pojo.page.PageResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 洗数控制器
 *
 * <AUTHOR>
 * @description
 * @date 2025/5/14
 */
@RestController
@RequestMapping("/qf/ai/biz/wash")
@Slf4j
public class WashController {

    @Autowired
    private AiChatRecordMapper aiChatRecordMapper;
    @Autowired
    private CsChatRecordNewMapper csChatRecordNewMapper;

    @RequestMapping("/deDuplicateAiRecord")
    public void deDuplicateAiRecord() {

        //去重AI回复记录
        List<AiChatRecordPo> aiChatRecordPos =
                aiChatRecordMapper.selectList(new AiChatRecordPo());

        aiChatRecordPos.forEach(po -> {
            String msgRecordId = po.getMsgRecordId();
            AiChatRecordPo query = new AiChatRecordPo();
            query.setMsgRecordId(msgRecordId);
            List<AiChatRecordPo> oldList = aiChatRecordMapper.selectList(query);
            if (oldList.size() > 1) {
                AiChatRecordPo aiChatRecordPo = oldList.get(0);
                String recordNo = aiChatRecordPo.getRecordNo();
                aiChatRecordMapper.deleteByRecordNo(recordNo);
                log.info("删除重复记录,记录编号:{},消息ID:{}", recordNo,msgRecordId);
            }
        });
    }

    @RequestMapping("/washMsgHistory")
    public void washMsgHistory() {

        log.info("开始洗数msgId");
        // 每批处理的数量
        int batchSize = 1000;
        // 总记录数
        int totalCount = 200000;
        // 计算总页数
        int totalPage = PageUtil.totalPage(totalCount, batchSize);
        log.info("计算出总页数:{}", totalPage);

        for (int pageNum = 1; pageNum <= totalPage; pageNum++) {
            AiChatRecordQueryPo queryPo = new AiChatRecordQueryPo();
            queryPo.setPageNum(pageNum);
            queryPo.setPageSize(batchSize);

            log.info("开始洗数msgId,分页参数为{}", JSON.toJSONString(queryPo));
            PageResult<AiChatRecordPo> pageResult = DBPageUtils.pageQuery(queryPo, aiChatRecordMapper::selectListByPage);
            List<AiChatRecordPo> aiChatRecordPos = pageResult.getList();
            log.info("洗数msgId,查询到的ai回复记录条数为{}", aiChatRecordPos.size());

            if (CollectionUtil.isEmpty(aiChatRecordPos)){
                break;
            }
            Map<Long, String> map = aiChatRecordPos.stream()
                    .collect(Collectors.toMap(key -> Long.valueOf(key.getMsgRecordId())
                            , AiChatRecordPo::getRecordNo
                            , (oldValue, newValue) -> newValue));
            List<CsChatRecordPo> csChatRecordPoList = csChatRecordNewMapper.queryByIdList(new ArrayList<>(map.keySet()));
            log.info("查询到的cs聊天记录条数为{}", csChatRecordPoList.size());
            csChatRecordPoList.forEach(po->{
                Long msgRecordId = po.getId();
                String msgId = po.getMsgId();
                AiChatRecordPo updatePo = new AiChatRecordPo();
                updatePo.setRecordNo(map.get(msgRecordId));
                updatePo.setMsgId(msgId);
                aiChatRecordMapper.update(updatePo);
            });
        }
    }

}
