package com.jd.qf.ai.server.sdk.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 腾讯云质检请求
 * <AUTHOR>
 * @description
 * @date 2025/4/10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InspectReq {

    /**
     * 质检类型
     */
    private String inspectContentType;

    /**
     * 角色
     */
    @NotBlank(message = "角色不能为空")
    private String speaker;

    /**
     * 上下文
     */
    @NotBlank(message = "上下文不能为空")
    private String context;

    /**
     * 上下文md5 key(做缓存用)
     */
    private String contextMd5Key;
}
