package com.jd.qf.ai.agent.core.api.inspect.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 腾讯云质检返回值
 * <AUTHOR>
 * @description
 * @date 2025/4/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InspectResponse {

    /**
     * 编码
     */
    private String code;

    /**
     * 数据
     */
    private String data;


    /**
     * 图片类型
     */
    private String pictureType;

    /**
     * 是否是二维码
     */
    private String hasQrCode;
}
