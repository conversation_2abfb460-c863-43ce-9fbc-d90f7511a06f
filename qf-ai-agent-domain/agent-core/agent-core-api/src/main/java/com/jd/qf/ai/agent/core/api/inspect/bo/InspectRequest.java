package com.jd.qf.ai.agent.core.api.inspect.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 腾讯云质检请求
 * <AUTHOR>
 * @description
 * @date 2025/4/10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InspectRequest {

    /**
     * 角色
     */
    @NotBlank(message = "角色不能为空")
    private String speaker;

    /**
     * 上下文
     */
    @NotBlank(message = "上下文不能为空")
    private String context;

    /**
     * 上下文md5 key(做缓存用)
     */
    private String contextMd5Key;

    /**
     * 质检类型
     * @see com.jd.qf.ai.server.common.pojo.enums.InspectContentTypeEnum
     */
    private String inspectContentType;
}
