package com.jd.qf.ai.agent.core.service.inspect;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.jd.qf.ai.agent.core.api.inspect.InspectService;
import com.jd.qf.ai.agent.core.api.inspect.bo.InspectRequest;
import com.jd.qf.ai.agent.core.api.inspect.bo.InspectResponse;
import com.jd.qf.ai.agent.core.converter.InspectConverter;
import com.jd.qf.ai.server.common.pojo.enums.InspectContentTypeEnum;
import com.jd.qf.ai.server.infrastructure.rpc.sec.LlmSecureRpcService;
import com.jd.qf.ai.server.infrastructure.rpc.sec.dto.LlmSecureRequest;
import com.jd.qf.ai.server.infrastructure.rpc.tecent.TencentCloudRpcService;
import com.jd.qf.ai.server.infrastructure.rpc.tecent.dto.ImageInspectAnalysis;
import com.jd.qf.ai.server.infrastructure.rpc.tecent.dto.TencentInspectRequest;
import com.jd.security.llmsec.core.session.Role;
import com.jdt.open.capability.client.redis.OpenRedisClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static com.jd.qf.ai.agent.common.constants.InspectConstants.SUCCESS_CODE;
import static com.jd.qf.ai.agent.common.constants.RedisPrefixConstants.MESSAGE_INSPECT;


/**
 * 质检服务实现
 *
 * <AUTHOR>
 * @description
 * @date 2025/4/10
 */
@Slf4j
@Service
public class InspectServiceImpl implements InspectService {

    @Autowired
    private TencentCloudRpcService tencentCloudRpcService;
    @Autowired
    private OpenRedisClient openRedisClient;
    @Autowired
    private LlmSecureRpcService llmSecureRpcService;

    @Override
    public Mono<InspectResponse> inspect(InspectRequest request) {

        String contextMd5Key = request.getContextMd5Key();
        String cacheKey = MESSAGE_INSPECT + contextMd5Key;
        if (StrUtil.isNotBlank(contextMd5Key)) {
            //查询缓存,若存在,则直接返回结果
            if (openRedisClient.get(cacheKey) != null) {
                log.debug("命中质检缓存,缓存key:{},结果为:{}", cacheKey, openRedisClient.get(cacheKey));
                return Mono.just(InspectResponse.builder().code(SUCCESS_CODE).data(openRedisClient.get(cacheKey)).build());
            }
        }

        if (InspectContentTypeEnum.IMAGE.getCode().equals(request.getInspectContentType())
                || InspectContentTypeEnum.GIF.getCode().equals(request.getInspectContentType())){
            return tencentCloudRpcService.imageInspect(InspectConverter.INSTANCE.toImageInspectRequest(request))
                    .map(resp->{
                        InspectResponse response=new InspectResponse();
                        ImageInspectAnalysis analysis = resp.getAnalysis();
                        response.setCode(resp.getCode());
                        response.setData(analysis.getData());
                        response.setPictureType(analysis.getImageType());
                        response.setHasQrCode(analysis.getQrCode());
                        return response;
                    })
                    .doOnSuccess(resp -> {
                        //若未命中缓存,则缓存一天失效
                        if (StrUtil.isNotBlank(contextMd5Key)) {
                            log.info("缓存图片质检结果,缓存key:{},结果为:{}", cacheKey, resp.getData());
                            openRedisClient.setEx(cacheKey, resp.getData(), 1, TimeUnit.DAYS);
                        }
                    });
        }

        //若缓存不存在,需要先请求言安接口,然后请求腾讯云的质检接口
        LlmSecureRequest secureRequest = new LlmSecureRequest();
        secureRequest.setRole(Role.user);
        secureRequest.setInput(request.getContext());
        secureRequest.setConversionId(UUID.randomUUID().toString());
        // 先创建请求对象，避免重复创建
        TencentInspectRequest defaultInspectRequest = InspectConverter.INSTANCE.to(request);

        return llmSecureRpcService.checkWithAllResp(secureRequest)
                // 使用 filter 过滤掉 null 值，这样如果 secResp 为 null，会转换为 Mono.empty()
                .filter(Objects::nonNull)
                .map(secResp -> {
                    defaultInspectRequest.setSecResult(JSON.toJSONString(secResp));
                    return defaultInspectRequest;
                })
                // 处理 Mono.empty() 或 secResp 为 null 的情况
                .switchIfEmpty(Mono.defer(() -> {
                    log.warn("言安接口返回了Mono.empty()或结果为null,给腾讯云质检接口降流结果");

                    defaultInspectRequest.setSecResult(getLimitJson());
                    return Mono.just(defaultInspectRequest);
                }))
                .flatMap(inspectRequest -> tencentCloudRpcService.inspect(inspectRequest)
                .flatMap(resp -> Mono.just(InspectConverter.INSTANCE.to(resp)))
                .doOnSuccess(resp -> {
                    //若未命中缓存,则缓存一天失效
                    if (StrUtil.isNotBlank(contextMd5Key)) {
                        log.debug("缓存质检结果,缓存key:{},结果为:{}", cacheKey, resp.getData());
                        openRedisClient.setEx(cacheKey, resp.getData(), 1, TimeUnit.DAYS);
                    }
                }));
    }

    /**
     * 获取固定的限流报文传给下游
     */
    private static String getLimitJson() {

        return """
                {"code":0,"cost":16,"data":[{"checkedContent":"没问题","requests":[{"ext":{"_process_node_":"11.61.162.241/http-nio-8080-exec-54","_process_time_":1745300286130},"fromRole":"user","sessionId":"2a8bdcf8-5b97-4f29-99b5-72228547ae56","toRole":"user"}],"riskCheckName":"redmodel20240802","riskCheckResult":{"type":"single_label_pred","riskCode":0,"riskMessage":"0正常文本","srcName":"redmodel20240802","cost":9,"probability":0.00001,"detail":[{"riskCode":1004,"riskMessage":"涉赌","probability":0.000010000003},{"riskCode":0,"riskMessage":"正常文本","probability":1.00001},{"riskCode":5001,"riskMessage":"违禁","probability":0.000010000003},{"riskCode":5002,"riskMessage":"虚假信息传播","probability":0.000010000003},{"riskCode":201,"riskMessage":"提示词注入","probability":0.000010000003},{"riskCode":1003,"riskMessage":"涉黄","probability":0.000010000003},{"riskCode":1002,"riskMessage":"暴恐","probability":0.000010000003},{"riskCode":1001,"riskMessage":"涉政","probability":0.000010000003},{"riskCode":1006,"riskMessage":"辱骂","probability":0.000010000003},{"riskCode":4002,"riskMessage":"其他侵犯他人权益","probability":0.000010000003},{"riskCode":1005,"riskMessage":"涉毒","probability":0.000010000003},{"riskCode":3002,"riskMessage":"其他商业违法违规","probability":0.000010000003},{"riskCode":2001,"riskMessage":"歧视性内容","probability":0.000010000003}]},"riskCheckType":"single_label_pred","riskCode":0,"riskMessage":"0正常文本"}],"message":"ok"}""";
    }
}
