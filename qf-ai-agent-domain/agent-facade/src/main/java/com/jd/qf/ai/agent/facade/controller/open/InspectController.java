package com.jd.qf.ai.agent.facade.controller.open;

import com.jd.qf.ai.agent.core.api.inspect.InspectService;
import com.jd.qf.ai.agent.core.api.inspect.bo.InspectRequest;
import com.jd.qf.ai.server.common.pojo.request.BaseRequest;
import com.jd.qf.ai.server.sdk.request.InspectReq;
import com.jd.qf.ai.server.sdk.response.InspectResp;
import com.jdt.open.dto.GeneralResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * 质检控制器
 *
 * <AUTHOR>
 * @description
 * @date 2025/4/10
 */
@RestController
@RequestMapping("/qf/ai/inspect")
public class InspectController {

    @Autowired
    private InspectService inspectService;

    @PostMapping("/doInspect")
    public Mono<GeneralResponse<InspectResp>> doInspect(@RequestBody BaseRequest<InspectReq> request) {
        InspectReq params = request.getParams();
        InspectRequest inspectRequest = InspectRequest.builder()
                .context(params.getContext())
                .speaker(params.getSpeaker())
                .contextMd5Key(params.getContextMd5Key())
                .inspectContentType(params.getInspectContentType())
                .build();
        return inspectService.inspect(inspectRequest)
                .map(resp -> GeneralResponse.success(InspectResp.builder().code(resp.getCode()).data(resp.getData()).build()));
    }

}
