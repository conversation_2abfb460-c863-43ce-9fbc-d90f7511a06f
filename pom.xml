<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
    </parent>
    <groupId>com.jd.wdy</groupId>
    <artifactId>qf-ai-server</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <java.version>17</java.version>
        <sourceEncoding>UTF-8</sourceEncoding>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.version>1.0.0</project.version>
        <open-lab.version>2.3.3-SNAPSHOT</open-lab.version>
        <ducc.client.version>1.4.1</ducc.client.version>
        <netty.version>4.1.101.Final</netty.version>
        <reactor-netty.version>1.0.34</reactor-netty.version>
        <open.sdk.version>1.0.3-SNAPSHOT</open.sdk.version>
        <ai.bi.api.version>1.0.1-SNAPSHOT</ai.bi.api.version>
    </properties>
    <modules>
        <module>qf-ai-starter</module>
        <module>qf-ai-common</module>
        <module>qf-ai-agent-domain</module>
        <module>qf-ai-biz-domain</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.github.imfangs</groupId>
                <artifactId>dify-java-client</artifactId>
                <version>1.0.8</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>2.18.0-jdsec.rc2</version>
            </dependency>
            <dependency>
                <groupId>com.jd.wdy</groupId>
                <artifactId>agent-open-sdk</artifactId>
                <version>${open.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish</groupId>
                <artifactId>javax.el</artifactId>
                <version>3.0.0</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-bom</artifactId>
                <version>4.1.94.Final</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-handler</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.jdq</groupId>
                <artifactId>jdq4-clients</artifactId>
                <version>1.3.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.jd.jr.aks</groupId>
                <artifactId>aks-spring-config-security</artifactId>
                <version>1.2.4</version>
                <exclusions>
                    <exclusion>
                            <groupId>com.jd</groupId>
                            <artifactId>jsf</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.jd.security.llmsec</groupId>
                <artifactId>llm-sec-core</artifactId>
                <version>0.1.5</version>
                <exclusions>
                    <exclusion>
                        <artifactId>bcprov-jdk15on</artifactId>
                        <groupId>org.bouncycastle</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-compress</artifactId>
                        <groupId>org.apache.commons</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>poi</artifactId>
                        <groupId>org.apache.poi</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>poi-ooxml</artifactId>
                        <groupId>org.apache.poi</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>poi-ooxml-schemas</artifactId>
                        <groupId>org.apache.poi</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>profiler</artifactId>
                        <groupId>com.jd.ump</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>asm</artifactId>
                        <groupId>org.ow2.asm</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>fastjson2</artifactId>
                        <groupId>com.alibaba.fastjson2</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

<!--            athena依赖-->
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>athena-open-api</artifactId>
                <version>1.0.4</version>
            </dependency>

<!--            BI依赖-->
            <dependency>
                <groupId>com.jd.jrdp</groupId>
                <artifactId>agile-bi-url-api</artifactId>
                <version>1.0.5</version>
            </dependency>
            <!-- easyexcel start -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.3.4</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>4.1.2</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>4.1.2</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-schemas</artifactId>
                <version>4.1.2</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.xmlbeans</groupId>
                <artifactId>xmlbeans</artifactId>
                <version>3.1.0</version>
            </dependency>
            <!-- easyexcel end -->
            <dependency>
                <groupId>com.github.librepdf</groupId>
                <artifactId>openpdf</artifactId>
                <version>2.0.2</version>
            </dependency>

<!--            科技权限依赖-->
            <dependency>
                <groupId>com.jd.jr.app</groupId>
                <artifactId>permissions-api</artifactId>
                <version>1.2.7</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-collections4</artifactId>
                        <groupId>org.apache.commons</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-lang</artifactId>
                        <groupId>commons-lang</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

<!--            ducc依赖-->
            <dependency>
                <groupId>com.jd.laf.config</groupId>
                <artifactId>laf-config-client-jd-springboot-starter</artifactId>
                <version>${ducc.client.version}</version>
                <type>pom</type> <!-- type 不能省略 -->
            </dependency>
            <dependency>
                <groupId>com.jd.laf.config</groupId>
                <artifactId>laf-config-client-logging</artifactId>
                <version>${ducc.client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>2.14.2</version>
            </dependency>
            <!--            region 单元测试-->
            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>2.2.220</version>
            </dependency>
            <!--放这是为了统一junit版本号-->
            <dependency>
                <groupId>org.junit</groupId>
                <artifactId>junit-bom</artifactId>
                <version>5.9.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
            <!--            endregion-->
            <!--            region open-lab-->
            <dependency>
                <groupId>com.jdt</groupId>
                <artifactId>open-bom-17</artifactId>
                <version>1.0.1</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>com.jdt.open</groupId>
                <artifactId>open-exception</artifactId>
                <version>${open-lab.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdt.open</groupId>
                <artifactId>open-util</artifactId>
                <version>${open-lab.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jdt.open</groupId>
                <artifactId>open-dto</artifactId>
                <version>${open-lab.version}</version>
            </dependency>
            <!--            endregion-->
            <!--            region 内部模块-->
            <dependency>
                <groupId>com.jd.wdy</groupId>
                <artifactId>qf-ai-common-pojo</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>

<!--            agent域-->
            <dependency>
                <groupId>com.jd.wdy</groupId>
                <artifactId>agent-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.wdy</groupId>
                <artifactId>agent-core-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.wdy</groupId>
                <artifactId>agent-core-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.wdy</groupId>
                <artifactId>agent-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.wdy</groupId>
                <artifactId>agent-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.wdy</groupId>
                <artifactId>agent-facade</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!--            业务域-->
            <dependency>
                <groupId>com.jd.wdy</groupId>
                <artifactId>ai-biz-api</artifactId>
                <version>${ai.bi.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.wdy</groupId>
                <artifactId>ai-biz-core-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.wdy</groupId>
                <artifactId>ai-biz-core-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.wdy</groupId>
                <artifactId>ai-biz-core-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.wdy</groupId>
                <artifactId>ai-biz-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.wdy</groupId>
                <artifactId>ai-biz-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.wdy</groupId>
                <artifactId>ai-biz-facade</artifactId>
                <version>${project.version}</version>
            </dependency>


            <!--        endregion-->
            <!--            region 京东内部依赖-->
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>jsf</artifactId>
                <version>1.7.8-HOTFIX-T3</version>
                <exclusions>
                    <exclusion>
                        <artifactId>laf-config-client-jd</artifactId>
                        <groupId>com.jd.laf.config</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jd.jim.cli</groupId>
                <artifactId>jim-cli-spring</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-beans</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-all</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpclient</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpcore</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpcore-nio</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.httpcomponents</groupId>
                        <artifactId>httpasyncclient</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>traceholder</artifactId>
                        <groupId>com.jd</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>unitrouter</artifactId>
                        <groupId>com.jd</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-reload4j</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
                <version>2.1.12-HOTFIX-T5</version>
            </dependency>
            <!--            endregion-->
            <!--            region 外部jar-->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>2.1.0</version>
            </dependency>
            <!--            endregion-->
            <dependency>
                <groupId>io.projectreactor.netty</groupId>
                <artifactId>reactor-netty</artifactId>
                <version>${reactor-netty.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.netty</groupId>
                        <artifactId>netty-all</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!--    region 基础依赖以及构建配置-->
    <dependencies>
        <dependency>
            <groupId>com.jd.wdy</groupId>
            <artifactId>qf-ai-common-pojo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdt.open</groupId>
            <artifactId>open-lab-parent</artifactId>
            <version>${open-lab.version}</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>1.5.2.Final</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>1.5.2.Final</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <!-- API, java.xml.bind module -->
        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
        </dependency>
        <!-- Runtime, com.sun.xml.bind module -->
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
        </dependency>
    </dependencies>
    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <profileActive>local</profileActive>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <profileActive>dev</profileActive>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <profileActive>test</profileActive>
            </properties>
        </profile>
        <profile>
            <id>pre</id>
            <properties>
                <profileActive>pre</profileActive>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profileActive>prod</profileActive>
            </properties>
        </profile>
    </profiles>
    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>Release Repository</name>
            <url>https://artifactory.jd.com/libs-releases</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>JD maven2 repository-snapshots</name>
            <url>https://artifactory.jd.com/libs-snapshots</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <finalName>qf-ai-server</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.7</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>target/classes</outputDirectory>
                            <useDefaultDelimiters>true</useDefaultDelimiters>
                            <encoding>${sourceEncoding}</encoding>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.apache.maven.shared</groupId>
                        <artifactId>maven-filtering</artifactId>
                        <version>1.3</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${sourceEncoding}</encoding>
                    <skip>true</skip>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>1.5.3.Final</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <version>3.0.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>addTestSources</goal>
                            <goal>compileTests</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <testSources>
                        <testSource>
                            <directory>${project.basedir}/src/test/java</directory>
                            <includes>
                                <include>**/*.groovy</include>
                            </includes>
                        </testSource>
                    </testSources>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.1.2</version>
                <configuration>
                    <useFile>false</useFile>
                    <basedir>${project.basedir}/src/test/java</basedir>
                    <includes>
                        <include>**/*Test.groovy</include>
                        <include>**/*Test.java</include>
                    </includes>
                    <!--suppress UnresolvedMavenProperty -->
                    <argLine>
                        ${argLine} -Xmx2048m
                        --add-exports java.base/sun.security.action=ALL-UNNAMED
                        --add-opens java.base/java.lang=ALL-UNNAMED
                        --add-opens java.base/java.math=ALL-UNNAMED
                        --add-opens java.base/java.util=ALL-UNNAMED
                        --add-opens java.base/sun.util.calendar=ALL-UNNAMED
                        --add-opens java.base/java.util.concurrent=ALL-UNNAMED
                        --add-opens java.base/java.util.concurrent.locks=ALL-UNNAMED
                        --add-opens java.base/java.security=ALL-UNNAMED
                        --add-opens java.base/jdk.internal.loader=ALL-UNNAMED
                        --add-exports java.base/sun.reflect.annotation=ALL-UNNAMED
                        --add-exports java.base/sun.util.calendar=ALL-UNNAMED
                        --add-opens java.management/com.sun.jmx.mbeanserver=ALL-UNNAMED
                        --add-opens java.base/java.net=ALL-UNNAMED
                        --add-opens java.base/sun.nio.ch=ALL-UNNAMED
                        --add-opens java.base/java.time=ALL-UNNAMED
                    </argLine>
                    <parallel>methods</parallel>
                    <threadCount>10</threadCount>
                    <testFailureIgnore>true</testFailureIgnore>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-report-plugin</artifactId>
                <version>3.1.2</version>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.8</version>
                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>post-unit-test</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <configuration>
                            <dataFile>target/jacoco.exec</dataFile>
                            <outputDirectory>target/site</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>

    </build>
    <!--    endregion-->
</project>